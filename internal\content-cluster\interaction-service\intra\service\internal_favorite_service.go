package service

import (
	"context"
	"fmt"

	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	"pxpat-backend/internal/content-cluster/interaction-service/repository"
)

// InternalFavoriteService 内部收藏服务实现
type InternalFavoriteService struct {
	favoriteFolderRepo repository.FavoriteFolderRepository
	favoriteItemRepo   repository.FavoriteItemRepository
}

// NewInternalFavoriteService 创建内部收藏服务实例
func NewInternalFavoriteService(
	favoriteFolderRepo repository.FavoriteFolderRepository,
	favoriteItemRepo repository.FavoriteItemRepository,
) *InternalFavoriteService {
	return &InternalFavoriteService{
		favoriteFolderRepo: favoriteFolderRepo,
		favoriteItemRepo:   favoriteItemRepo,
	}
}

// AddToFavoriteInternal 内部添加到收藏夹
func (s *InternalFavoriteService) AddToFavoriteInternal(ctx context.Context, userKSUID string, contentKSUID string, contentType model.ContentType, folderID string) error {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Str("content_type", string(contentType)).
		Str("folder_id", folderID).
		Msg("Internal add to favorite")

	// 如果没有指定收藏夹，使用默认收藏夹
	if folderID == "" {
		defaultFolder, err := s.favoriteFolderRepo.GetDefaultFolder(ctx, userKSUID)
		if err != nil {
			// 创建默认收藏夹
			defaultFolder = model.NewDefaultFavoriteFolder(userKSUID)
			err = s.favoriteFolderRepo.Create(ctx, defaultFolder)
			if err != nil {
				log.Error().Err(err).
					Str("user_ksuid", userKSUID).
					Msg("Failed to create default folder for user")
				return fmt.Errorf("failed to create default folder: %w", err)
			}
		}
		folderID = defaultFolder.FavoriteFolderID
	}

	// 检查是否已经收藏
	exists, err := s.favoriteItemRepo.ExistsByUserAndContent(ctx, userKSUID, contentKSUID)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Str("folder_id", folderID).
			Msg("Failed to check favorite item existence")
		return fmt.Errorf("failed to check favorite existence: %w", err)
	}

	if exists {
		log.Warn().
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Str("folder_id", folderID).
			Msg("Content already in favorite folder")
		return nil // 已经收藏，不需要重复添加
	}

	// 添加到收藏夹
	//err = s.favoriteFolderRepo.(ctx, userKSUID, contentKSUID, contentType, folderID)
	//if err != nil {
	//	log.Error().Err(err).
	//		Str("user_ksuid", userKSUID).
	//		Str("content_ksuid", contentKSUID).
	//		Str("folder_id", folderID).
	//		Msg("Failed to add to favorite")
	//	return fmt.Errorf("failed to add to favorite: %w", err)
	//}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Str("folder_id", folderID).
		Msg("Successfully added to favorite")

	return nil
}

// RemoveFromFavoriteInternal 内部从收藏夹移除
func (s *InternalFavoriteService) RemoveFromFavoriteInternal(ctx context.Context, userKSUID string, contentKSUID string, folderID string) error {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Str("folder_id", folderID).
		Msg("Internal remove from favorite")

	// 如果没有指定收藏夹，从默认收藏夹移除
	if folderID == "" {
		defaultFolder, err := s.favoriteFolderRepo.GetDefaultFolder(ctx, userKSUID)
		if err != nil {
			log.Error().Err(err).
				Str("user_ksuid", userKSUID).
				Msg("Failed to get default folder")
			return fmt.Errorf("failed to get default folder: %w", err)
		}
		folderID = defaultFolder.FavoriteFolderID
	}

	// 从收藏夹移除
	//err := s.favoriteRepo.RemoveFromFavorite(ctx, userKSUID, contentKSUID)
	//if err != nil {
	//	log.Error().Err(err).
	//		Str("user_ksuid", userKSUID).
	//		Str("content_ksuid", contentKSUID).
	//		Str("folder_id", folderID).
	//		Msg("Failed to remove from favorite")
	//	return fmt.Errorf("failed to remove from favorite: %w", err)
	//}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Str("folder_id", folderID).
		Msg("Successfully removed from favorite")

	return nil
}

// CheckFavoriteStatusInternal 内部检查收藏状态
func (s *InternalFavoriteService) CheckFavoriteStatusInternal(ctx context.Context, userKSUID string, contentKSUID string) (bool, []string, error) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Msg("Internal check favorite status")

	// 获取收藏项
	items, err := s.favoriteItemRepo.GetByUserAndContent(ctx, userKSUID, contentKSUID)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Msg("Failed to get favorite items")
		return false, nil, fmt.Errorf("failed to get favorite items: %w", err)
	}

	if len(items) == 0 {
		return false, nil, nil
	}

	// 提取收藏夹ID列表
	folderIDs := make([]string, len(items))
	for i, item := range items {
		folderIDs[i] = item.FavoriteFolderID
	}

	return true, folderIDs, nil
}

// GetUserFavoriteStatsInternal 内部获取用户收藏统计
func (s *InternalFavoriteService) GetUserFavoriteStatsInternal(ctx context.Context, userKSUID string) (*dto.GetFavoriteStatsResponse, error) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Msg("Internal get user favorite stats")

	// 获取收藏夹统计
	folderStats, err := s.favoriteFolderRepo.GetUserFolderStats(ctx, userKSUID)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("Failed to get folder stats")
		return nil, fmt.Errorf("failed to get folder stats: %w", err)
	}

	// 注意：itemStats暂时不使用，因为folderStats已经包含了所需信息

	// 直接返回folderStats，因为它已经包含了正确的结构
	return folderStats, nil
}

// BatchCheckFavoriteStatusInternal 内部批量检查收藏状态
func (s *InternalFavoriteService) BatchCheckFavoriteStatusInternal(ctx context.Context, userKSUID string, contentKSUIDs []string) (map[string]bool, error) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Int("content_count", len(contentKSUIDs)).
		Msg("Internal batch check favorite status")

	if len(contentKSUIDs) == 0 {
		return make(map[string]bool), nil
	}

	// 批量检查收藏状态
	favoriteMap, err := s.favoriteItemRepo.BatchCheckFavoriteStatus(ctx, userKSUID, contentKSUIDs)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("Failed to batch check favorite status")
		return nil, fmt.Errorf("failed to batch check favorite status: %w", err)
	}

	return favoriteMap, nil
}
