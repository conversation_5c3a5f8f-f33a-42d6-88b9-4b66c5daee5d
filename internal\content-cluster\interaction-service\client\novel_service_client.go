package client

import (
	"fmt"
	"time"

	"pxpat-backend/pkg/httpclient"
)

// NovelServiceClient 小说服务客户端接口
type NovelServiceClient interface {
	// GetContentInfo 获取内容信息
	GetContentInfo(contentKSUID string) (*ContentInfo, error)
	// GetContentByKSUID 根据KSUID获取内容信息（用于收藏功能）
	GetContentByKSUID(contentKSUID string) (*ContentInfo, error)
	// BatchGetContentsByKSUIDs 批量获取内容信息
	BatchGetContentsByKSUIDs(contentKSUIDs []string) (map[string]*ContentInfo, error)
	// UpdateContentFavoriteCount 更新内容收藏数
	UpdateContentFavoriteCount(contentKSUID string, increment bool) error
}

// novelServiceClient 小说服务客户端实现
type novelServiceClient struct {
	httpClient *httpclient.HTTPClient
}

// NovelServiceConfig 小说服务客户端配置
type NovelServiceConfig struct {
	BaseURL string
	Timeout time.Duration
}

// NewNovelServiceClient 创建小说服务客户端
func NewNovelServiceClient(config NovelServiceConfig) NovelServiceClient {
	return &novelServiceClient{
		httpClient: httpclient.NewHTTPClient(httpclient.ClientConfig{
			BaseURL:          config.BaseURL,
			Timeout:          config.Timeout,
			RetryCount:       3,
			RetryWaitTime:    1 * time.Second,
			RetryMaxWaitTime: 5 * time.Second,
		}),
	}
}

// GetContentInfo 获取内容信息
func (c *novelServiceClient) GetContentInfo(contentKSUID string) (*ContentInfo, error) {
	var response struct {
		Code int         `json:"code"`
		Data ContentInfo `json:"data"`
	}

	err := c.httpClient.Get(fmt.Sprintf("/api/v1/novels/%s", contentKSUID), &response)
	if err != nil {
		return nil, fmt.Errorf("failed to get novel info: %w", err)
	}

	if response.Code != 200 {
		return nil, fmt.Errorf("novel service returned error code: %d", response.Code)
	}

	return &response.Data, nil
}

// GetContentByKSUID 根据KSUID获取内容信息（用于收藏功能）
func (c *novelServiceClient) GetContentByKSUID(contentKSUID string) (*ContentInfo, error) {
	// 这个方法与GetContentInfo相同，为了保持接口一致性而添加
	return c.GetContentInfo(contentKSUID)
}

// BatchGetContentsByKSUIDs 批量获取内容信息
func (c *novelServiceClient) BatchGetContentsByKSUIDs(contentKSUIDs []string) (map[string]*ContentInfo, error) {
	var response struct {
		Code int                     `json:"code"`
		Data map[string]*ContentInfo `json:"data"`
	}

	requestBody := struct {
		ContentKSUIDs []string `json:"content_ksuids"`
	}{
		ContentKSUIDs: contentKSUIDs,
	}

	err := c.httpClient.Post("/api/v1/novels/batch", requestBody, &response)
	if err != nil {
		return nil, fmt.Errorf("failed to batch get novels: %w", err)
	}

	if response.Code != 200 {
		return nil, fmt.Errorf("novel service returned error code: %d", response.Code)
	}

	return response.Data, nil
}

// UpdateContentFavoriteCount 更新内容收藏数
func (c *novelServiceClient) UpdateContentFavoriteCount(contentKSUID string, increment bool) error {
	var response struct {
		Code int `json:"code"`
	}

	requestBody := struct {
		Increment bool `json:"increment"`
	}{
		Increment: increment,
	}

	err := c.httpClient.Put(fmt.Sprintf("/api/v1/novels/%s/favorite-count", contentKSUID), requestBody, &response)
	if err != nil {
		return fmt.Errorf("failed to update novel favorite count: %w", err)
	}

	if response.Code != 200 {
		return fmt.Errorf("novel service returned error code: %d", response.Code)
	}

	return nil
}
