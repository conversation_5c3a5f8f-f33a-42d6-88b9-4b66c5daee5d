package service

import (
	"context"

	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/interaction-service/client"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	"pxpat-backend/internal/content-cluster/interaction-service/repository"
	"pxpat-backend/pkg/errors"
)

type PlayHistoryService struct {
	playHistoryRepo    repository.PlayHistoryRepository
	userServiceClient  client.UserServiceClient
	videoServiceClient client.VideoServiceClient
}

func NewPlayHistoryService(
	playHistoryRepo repository.PlayHistoryRepository,
	userServiceClient client.UserServiceClient,
	videoServiceClient client.VideoServiceClient,
) *PlayHistoryService {
	return &PlayHistoryService{
		playHistoryRepo:    playHistoryRepo,
		userServiceClient:  userServiceClient,
		videoServiceClient: videoServiceClient,
	}
}

// UpdatePlayHistory 更新播放历史记录
func (s *PlayHistoryService) UpdatePlayHistory(ctx context.Context, req *dto.UpdatePlayHistoryRequest) (*dto.UpdatePlayHistoryResponse, error) {
	log.Info().
		Str("user_ksuid", req.UserKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Str("content_type", req.ContentType).
		Int64("play_duration", req.PlayDuration).
		Msg("开始更新播放历史记录")

	// 验证播放时长不能为负数
	if req.PlayDuration < 0 {
		log.Error().
			Str("user_ksuid", req.UserKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Str("content_type", req.ContentType).
			Int64("play_duration", req.PlayDuration).
			Msg("播放时长不能为负数")
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, nil)
	}

	// 验证内容类型
	if !model.IsValidContentType(model.ContentType(req.ContentType)) {
		log.Error().
			Str("user_ksuid", req.UserKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Str("content_type", req.ContentType).
			Msg("无效的内容类型")
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, nil)
	}

	// 更新播放历史记录
	playHistory, isNew, err := s.playHistoryRepo.UpdatePlayHistory(ctx, req.UserKSUID, req.ContentKSUID, req.ContentType, req.PlayDuration)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", req.UserKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Str("content_type", req.ContentType).
			Int64("play_duration", req.PlayDuration).
			Msg("更新播放历史记录失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.UPDATE_RECORD_ERROR, err)
	}

	// 构建响应
	response := &dto.UpdatePlayHistoryResponse{
		PlayHistoryItemID: playHistory.PlayHistoryItemID,
		UserKSUID:         playHistory.UserKSUID,
		ContentKSUID:      playHistory.ContentKSUID,
		ContentType:       string(playHistory.ContentType),
		PlayDuration:      playHistory.PlayDuration,
		IsNew:             isNew,
	}

	log.Info().
		Str("user_ksuid", req.UserKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Str("content_type", req.ContentType).
		Int64("play_duration", req.PlayDuration).
		Str("play_history_item_id", playHistory.PlayHistoryItemID).
		Bool("is_new", isNew).
		Msg("更新播放历史记录成功")

	return response, nil
}

// GetMyPlayHistory 获取我的播放历史记录
func (s *PlayHistoryService) GetMyPlayHistory(ctx context.Context, userKSUID string, req *dto.GetMyPlayHistoryRequest) (*dto.GetMyPlayHistoryResponse, error) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Str("content_type", req.ContentType).
		Msg("开始获取用户播放历史记录")

	// 参数校验
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}

	// 获取用户播放历史记录
	playHistories, total, err := s.playHistoryRepo.GetUserPlayHistories(ctx, userKSUID, req.Page, req.PageSize, req.ContentType)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Int("page", req.Page).
			Int("page_size", req.PageSize).
			Str("content_type", req.ContentType).
			Msg("获取用户播放历史记录失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 提取内容KSUID并批量获取内容信息
	contentKSUIDs := make([]string, len(playHistories))
	for i, playHistory := range playHistories {
		contentKSUIDs[i] = playHistory.ContentKSUID
	}

	// 批量获取内容信息
	contentInfoMap := s.batchGetContentInfo(ctx, contentKSUIDs)

	// 转换为DTO
	playHistoryItems := make([]dto.PlayHistoryItemDTO, len(playHistories))
	for i, playHistory := range playHistories {
		playHistoryItems[i] = dto.PlayHistoryItemDTO{
			PlayHistoryItemID: playHistory.PlayHistoryItemID,
			ContentKSUID:      playHistory.ContentKSUID,
			ContentType:       string(playHistory.ContentType),
			PlayDuration:      playHistory.PlayDuration,
			ContentInfo:       contentInfoMap[playHistory.ContentKSUID],
			CreatedAt:         playHistory.CreatedAt,
			UpdatedAt:         playHistory.UpdatedAt,
		}
	}

	// 计算分页信息
	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))
	hasNext := req.Page < totalPages
	hasPrev := req.Page > 1

	response := &dto.GetMyPlayHistoryResponse{
		PlayHistories: playHistoryItems,
		Total:         total,
		Page:          req.Page,
		PageSize:      req.PageSize,
		TotalPages:    totalPages,
		HasNext:       hasNext,
		HasPrev:       hasPrev,
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Str("content_type", req.ContentType).
		Int64("total", total).
		Int("count", len(playHistoryItems)).
		Msg("获取用户播放历史记录成功")

	return response, nil
}

// DeleteMyPlayHistory 删除指定的播放历史记录
func (s *PlayHistoryService) DeleteMyPlayHistory(ctx context.Context, userKSUID string, req *dto.DeletePlayHistoryRequest) (*dto.DeletePlayHistoryResponse, error) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Interface("play_history_item_ids", req.PlayHistoryItemIDs).
		Msg("开始删除指定的用户播放历史记录")

	// 参数校验
	if len(req.PlayHistoryItemIDs) == 0 {
		log.Error().
			Str("user_ksuid", userKSUID).
			Msg("要删除的ID列表为空")
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, nil)
	}

	// 删除指定的播放历史记录
	deletedCount, err := s.playHistoryRepo.DeleteByIDs(ctx, userKSUID, req.PlayHistoryItemIDs)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Interface("play_history_item_ids", req.PlayHistoryItemIDs).
			Msg("删除指定的用户播放历史记录失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.DELETE_RECORD_ERROR, err)
	}

	response := &dto.DeletePlayHistoryResponse{
		DeletedCount: deletedCount,
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Interface("play_history_item_ids", req.PlayHistoryItemIDs).
		Int64("deleted_count", deletedCount).
		Msg("删除指定的用户播放历史记录成功")

	return response, nil
}

// ClearAllMyPlayHistory 清空我的播放历史记录
func (s *PlayHistoryService) ClearAllMyPlayHistory(ctx context.Context, userKSUID string) (*dto.DeletePlayHistoryResponse, error) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Msg("开始清空用户播放历史记录")

	// 清空用户播放历史记录
	deletedCount, err := s.playHistoryRepo.ClearAllUserPlayHistories(ctx, userKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Msg("清空用户播放历史记录失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.DELETE_RECORD_ERROR, err)
	}

	response := &dto.DeletePlayHistoryResponse{
		DeletedCount: deletedCount,
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Int64("cleared_count", deletedCount).
		Msg("清空用户播放历史记录成功")

	return response, nil
}

// batchGetContentInfo 批量获取内容信息
func (s *PlayHistoryService) batchGetContentInfo(ctx context.Context, contentKSUIDs []string) map[string]*dto.ExternalContentInfo {
	if len(contentKSUIDs) == 0 {
		return make(map[string]*dto.ExternalContentInfo)
	}

	// 调用video-service获取内容信息
	contentInfoMap := make(map[string]*dto.ExternalContentInfo)

	// TODO: 这里需要实现调用video-service的批量获取内容信息接口
	// 暂时返回空的map，后续实现video-service客户端时补充
	log.Warn().
		Strs("content_ksuids", contentKSUIDs).
		Msg("批量获取内容信息功能待实现")

	return contentInfoMap
}
