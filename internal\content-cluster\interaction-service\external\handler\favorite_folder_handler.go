package handler

import (
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"net/http"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/external/service"
	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/ksuid"
	globalTypes "pxpat-backend/pkg/types"
)

// FavoriteFolderHandler 收藏夹处理器
type FavoriteFolderHandler struct {
	favoriteFolderService *service.FavoriteFolderService
}

// NewFavoriteFolderHandler 创建收藏夹处理器
func NewFavoriteFolderHandler(favoriteFolderService *service.FavoriteFolderService) *FavoriteFolderHandler {
	return &FavoriteFolderHandler{
		favoriteFolderService: favoriteFolderService,
	}
}

// CreateFolder 创建收藏夹
func (h *FavoriteFolderHandler) CreateFolder(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)

	var req dto.CreateFavoriteFolderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("创建收藏夹请求参数无效")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 参数验证
	if req.DirName == "" {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	folder, err := h.favoriteFolderService.CreateFolder(c.Request.Context(), userKSUID, &req)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("folder_name", req.DirName).
			Msg("创建收藏夹失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    errors.INTERNAL_ERROR,
			Message: err.Error(),
		})
		return
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("folder_id", folder.FavoriteFolderID).
		Str("folder_name", folder.DirName).
		Msg("成功创建收藏夹")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: folder,
	})
}

// UpdateFolder 更新收藏夹
func (h *FavoriteFolderHandler) UpdateFolder(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)
	folderID := c.Param("folder_id")

	if folderID == "" {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	var req dto.UpdateFavoriteFolderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("folder_id", folderID).
			Msg("更新收藏夹请求参数无效")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	folder, err := h.favoriteFolderService.UpdateFolder(c.Request.Context(), userKSUID, folderID, &req)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("folder_id", folderID).
			Msg("更新收藏夹失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    errors.INTERNAL_ERROR,
			Message: err.Error(),
		})
		return
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("folder_id", folderID).
		Msg("成功更新收藏夹")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: folder,
	})
}

// DeleteFolder 删除收藏夹
func (h *FavoriteFolderHandler) DeleteFolder(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)
	folderID := c.Param("folder_id")

	if folderID == "" {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	gErr := h.favoriteFolderService.DeleteFolder(c.Request.Context(), userKSUID, folderID)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("user_ksuid", userKSUID).
			Str("folder_id", folderID).
			Msg("删除收藏夹失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    gErr.Code,
			Message: gErr.Error(),
		})
		return
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("folder_id", folderID).
		Msg("成功删除收藏夹")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
	})
}

// GetFolder 获取收藏夹详情
func (h *FavoriteFolderHandler) GetFolder(c *gin.Context) {
	userKSUID, _ := ksuid.TryGetKSUID(c)
	folderID := c.Param("folder_id")

	if folderID == "" {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	folder, gErr := h.favoriteFolderService.GetFolder(c.Request.Context(), userKSUID, folderID)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("user_ksuid", userKSUID).
			Str("folder_id", folderID).
			Msg("获取收藏夹失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    gErr.Code,
			Message: gErr.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: folder,
	})
}

// GetFolders 获取收藏夹列表
func (h *FavoriteFolderHandler) GetFolders(c *gin.Context) {
	userKSUID, _ := ksuid.TryGetKSUID(c)

	// 解析查询参数
	var req dto.GetFavoriteFoldersRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("获取收藏夹列表请求参数无效")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}
	if req.UserKSUID == "" {
		req.UserKSUID = userKSUID
	}

	response, gErr := h.favoriteFolderService.GetFolders(c.Request.Context(), userKSUID, &req)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("user_ksuid", userKSUID).
			Msg("获取收藏夹列表失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    gErr.Code,
			Message: gErr.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}

// GetFavoriteStats 获取收藏统计
func (h *FavoriteFolderHandler) GetFavoriteStats(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)

	stats, err := h.favoriteFolderService.GetFavoriteStats(c.Request.Context(), userKSUID)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("获取收藏统计失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    errors.INTERNAL_ERROR,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: stats,
	})
}
